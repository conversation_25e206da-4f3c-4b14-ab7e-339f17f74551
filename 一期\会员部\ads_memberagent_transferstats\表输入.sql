WITH temp_cust AS (
  SELECT
        DISTINCT
        b.mdlg_usr_wrd,
        a.agent_no,
        a.compy_name,
        b.cust_wrd
        FROM (
        SELECT
            *,
            ROW_NUMBER() OVER (PARTITION BY a.cust_no ORDER BY a.update_time DESC) AS rn
        FROM ods.ods_bl_agent_info a
        WHERE a.dt = '${dmp_day}'
        ) a
        LEFT JOIN (
        SELECT * FROM dim.dim_pty_cust_usr_rel
        WHERE dt='${dmp_day}'
        AND edw_end_dt='20991231'
        ) b ON 'BJHL'||a.cust_no=b.cust_wrd
        WHERE a.rn = 1 --取最新的一条
),
prj as (
select
       a.txn_svc_mber_id,
       a.prj_bsn_tp_cd,
       a.prj_id,
       a.bsn_prj_wrd, 
       a.prj_wrd,
       b.delvry_dt as deal_dt,
       b.deal_amt,
       b.bsn_deal_rec_id
from dwd.dwd_prj_fct a
left join dwd.dwd_evt_deal_rec_fct b 
  on a.prj_wrd = b.prj_wrd
  and a.dt = b.dt
where a.dt = '${dmp_day}'
  and a.prj_bsn_tp_cd in ('1F', '1B') 
union all
select
       a.txn_svc_mber_id,
       a.prj_bsn_tp_cd,
       a.prj_id,
       a.bsn_prj_wrd,
       a.prj_wrd,
       b.bsn_rec_deal_dt as deal_dt,
       b.deal_amt,
       b.bsn_deal_rec_id
from dwd.dwd_prj_fct a
         left join (select * from dwd.dwd_evt_deal_rec_fct where dt='${dmp_day}') b on
 a.bsn_prj_wrd = b.bsn_prj_wrd
where a.dt = '${dmp_day}'
  and a.prj_bsn_tp_cd in ('GQ', '1D', '1C', '1G') 
),
temp AS (
  SELECT
    a.prj_wrd,
    CASE
      WHEN prj_bsn_tp_cd IN ('GQ', '1D', '1C', '1G') THEN COALESCE(c.agent_no, '未知')
      WHEN prj_bsn_tp_cd = '1F' THEN COALESCE(e.agent_no, '未知')
      WHEN prj_bsn_tp_cd = '1B' THEN COALESCE(g.agent_no, '未知')
    END AS mber_org_id,
    CASE
      WHEN prj_bsn_tp_cd IN ('GQ', '1D', '1C', '1G') THEN COALESCE(c.compy_name, '未知')
      WHEN prj_bsn_tp_cd = '1F' THEN COALESCE(e.compy_name, '未知')
      WHEN prj_bsn_tp_cd = '1B' THEN COALESCE(g.compy_name, '未知')
    END AS mber_org_nm,
    a.prj_bsn_tp_cd,
   a.deal_dt,
    CASE
      WHEN a.prj_bsn_tp_cd = '1C' and e1.project_type = '1' THEN sum(t1.new_pucpl_cptl)/10000
      WHEN a.prj_bsn_tp_cd = '1C' and e1.project_type = '2' THEN e1.fnc_tot_amt_w_yuan
      ELSE a.deal_amt/10000
    END AS deal_amt
  FROM prj a
  LEFT JOIN dwd.dwd_evt_task_list_fct d ON a.prj_wrd = d.prj_wrd AND d.dt = '${dmp_day}'
  LEFT JOIN temp_cust e ON d.task_recver_cust_wrd = e.cust_wrd
  LEFT JOIN temp_cust c ON CONCAT('BJHL', a.txn_svc_mber_id) = c.mdlg_usr_wrd
  LEFT JOIN dwd.dwd_bid_task_list_info f ON a.prj_wrd = f.rltv_prj AND f.dt = '${dmp_day}'
  LEFT JOIN temp_cust g ON CONCAT('BJHL', f.tsk_rcv_side_unit) = g.cust_wrd
  -- LEFT JOIN dwd.dwd_ast_lease_prj_fct g ON a.bsn_prj_wrd = g.bsn_prj_wrd AND g.dt = '${dmp_day}'
  LEFT JOIN dwd.dwd_bulk_obj_prj_fct h ON a.bsn_prj_wrd = h.bsn_prj_wrd AND h.dt = '${dmp_day}'
  LEFT JOIN dwd.dwd_entp_incptl_deal_rec_fct t1 ON a.bsn_prj_wrd = t1.bsn_prj_wrd 
            AND a.bsn_deal_rec_id = CAST(t1.bsn_deal_rec_id AS String) 
            AND t1.dt = '${dmp_day}'
  left join (
              SELECT DISTINCT project_code,project_type,fnc_tot_amt_w_yuan 
                  FROM std.std_bjhl_tcgq_zzzsgpxm_d 
                  WHERE dt = '${dmp_day}') e1
          ON a.prj_id = e1.project_code AND a.prj_bsn_tp_cd = '1C'
  GROUP BY a.prj_wrd, a.prj_bsn_tp_cd, a.deal_dt, c.agent_no, 
           c.compy_name, e.agent_no, e.compy_name, g.agent_no, 
           g.compy_name, e1.project_type, e1.fnc_tot_amt_w_yuan,a.deal_amt
)
SELECT prj_bsn_tp_cd, deal_dt AS deal_date, mber_org_id AS agent_no, 
       mber_org_nm AS agent_mem, sum(deal_amt) AS deal_value_property
FROM temp
WHERE mber_org_id != '未知' AND deal_dt IS NOT NULL
GROUP BY deal_dt, mber_org_id, mber_org_nm, prj_bsn_tp_cd