select 
case when a.lit_exg_cd_dsc ='上海联合产权交易所' then '上海'
when a.lit_exg_cd_dsc ='广东联合产权交易中心' then '广东'
when a.lit_exg_cd_dsc ='重庆联合产权交易所' then '重庆'
when a.lit_exg_cd_dsc ='深圳联合产权交易所' then '深圳'
when a.lit_exg_cd_dsc ='山东产权交易中心' then '山东' end
as exch,
a.prj_id as ht_proj_no,
a.prj_nm as SUBJ_MATTER_NAME,
CASE WHEN c.src_cd_dsc = '国务院国资委监管' THEN '央企'
     WHEN c.src_cd_dsc = '中央其他部委监管' THEN '部委'
ELSE '' END as cust_class,
hqname as BELONG_GROUP,
sellername as SELLER_FINCER_NAME,
tfr_prc/10000 as HT_AMT_W_YUAN,
'' as AGENT_MEM,
lit_start_dt as INFO_DCLO_BEGIN_DT,
case when a.txn_cgy_cd_dsc in('资产转让','小宗实物') then '实物' 
    when a.txn_cgy_cd_dsc in('企业产权转让') then '产权转让' 
    when a.txn_cgy_cd_dsc in('企业增资') then '企业增资'  end  as PROJ_TYPE,
    c.src_cd_dsc as CUSTD_TYPE,
    '' PROJ_BELONG_DEPT_NAME,
    '' PROJ_PRINC_NAME,
    '' SRC_FLAG,
    current_date() UDT_TM,
    '爬虫系统' as DATA_SOURCE 
from dwd.dwd_stb_prj_fct a
left join ods.ods_pcxt_sellerinfo b on a.prj_id=b.projectcode and b.dt='${dmp_day}'
left join 
(select src_cd_val,src_cd_dsc from dim.dim_pub_cd_val_src_trgt_mpng where pub_cd_no='CD000042' and src_tab_eng_nm ='sdm.s0_bot_sellerinfo') c
on b.monitorName=c.src_cd_val
left anti join (
    select ht_proj_no from  ods.ods_bl_center_formal_dcl where dt = '${dmp_day}'
) d
on a.prj_id = d.ht_proj_no
where a.dt='${dmp_day}'
and a.lit_exg_cd_dsc in
('上海联合产权交易所','广东联合产权交易中心','重庆联合产权交易所','深圳联合产权交易所','山东产权交易中心')
and TO_DATE(SUBSTR(a.lit_start_dt,1,10)) >= DATE_SUB(TO_DATE(FROM_UNIXTIME(UNIX_TIMESTAMP('${dmp_day}', 'yyyyMMdd'), 'yyyy-MM-dd')), 35)